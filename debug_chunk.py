#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.code_validators import chunk_code

def debug_chunk_resources():
    """调试 resources.d.ts 文件的分块问题"""
    
    # 读取文件内容
    with open('resources.d.ts', 'r', encoding='utf-8') as f:
        code = f.read()
    
    path = 'resources.d.ts'
    
    print(f"文件路径: {path}")
    print(f"文件行数: {len(code.splitlines())}")
    print(f"文件大小: {len(code)} 字符")
    
    # 提取扩展名
    ext = path.split(".")[-1]
    print(f"提取的扩展名: {ext}")
    
    # 检查扩展名映射
    from utils.code_validators import extension_to_language
    if ext in extension_to_language:
        language = extension_to_language[ext]
        print(f"映射的语言: {language}")
    else:
        print("扩展名未在映射中找到")
        return
    
    try:
        # 尝试分块
        print("\n开始分块...")
        snippets = chunk_code(code, path)
        
        print(f"生成的分块数量: {len(snippets)}")
        
        for i, snippet in enumerate(snippets):
            print(f"\n分块 {i+1}:")
            print(f"  起始行: {snippet.start}")
            print(f"  结束行: {snippet.end}")
            print(f"  行数: {snippet.end - snippet.start + 1}")
            
            # 显示前几行内容
            content = snippet.get_snippet(add_ellipsis=False, add_lines=False)
            lines = content.split('\n')
            preview_lines = lines[:3]
            print(f"  内容预览: {preview_lines}")
            
    except Exception as e:
        print(f"分块失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_chunk_resources()
