interface Resources{
  "translation": {
    "Auto_Finish": "Auto-checkout",
    "Auto_Finish_NetworkError": "Network error. Checkout will be automatically processed once the network is stable, or you can retry now.",
    "Auto_Retry": "Retry",
    "Avatar_Selection_Page": "Choose your avatar",
    "Cancel_Mark_Btn": "Delete marks",
    "Chat_Link_Fallback_Text": "No group chats available to connect.",
    "Collect_Done_Toast": "Added to Collections",
    "Cycling_Duration": "Duration",
    "Cycling_Feature1": "One-way",
    "Cycling_Feature10": "Highlights",
    "Cycling_Feature2": "Loop",
    "Cycling_Feature3": "Flat",
    "Cycling_Feature4": "Slope",
    "Cycling_Feature5": "Morning",
    "Cycling_Feature6": "Night",
    "Cycling_Feature7": "Low traffic",
    "Cycling_Feature8": "Fresh air",
    "Cycling_Feature9": "Creative designs",
    "Cycling_LinktoGroup": "Connect to cycling groups",
    "Cycling_Multiple_Choice": "Multiple choice",
    "Cycling_Page_Title": "Cycling",
    "Cycling_Rate": "Current speed",
    "Cycling_Route_Tags": "Route tags",
    "Cycling_Type1": "Cycling path",
    "Default_Start_Text": "My Location",
    "Desc_Input_Fail": "Review failed. Please check the track description.",
    "Desc_Input_Hint": "Route description must be within 18 characters.",
    "Desc_Level1": "Up to 10 km, smooth roads. Great for beginners.\r\n",
    "Desc_Level2": "10–50 km, for experienced riders. Push your limits.\r\n",
    "Desc_Level3": "50 km and beyond, high endurance required. Steel glutes guaranteed!\r\n",
    "Difficulty_1_Desc": "Within 2 km – Suitable for beginners starting their running routine.",
    "Difficulty_2_Desc": "2–10 km – Ideal for experienced runners.",
    "Difficulty_3_Desc": "Over 10 km – Best for professional athletes.",
    "Duplicate_Add_Locations": "Duplicate locations cannot be added",
    "Fast_Speed_Text": "MAX",
    "Fast_Speed_Text_Running": "MAX",
    "Fetch_error_sxss_19939c6a": "Refresh",
    "Fetch_error_wljdfwqsxyxss_8d529abe": "Server connection failed. Tap the button below to refresh.",
    "Follow_Run_End_Point": "Destination",
    "Follow_Run_Start_Point": "Start",
    "Follow_Track_Group_Chat_Tab": "Join Running Group Chats",
    "Function_Disable_POI": "Feature is not supported in the current region",
    "Groupchat_bdqzwql": "No local group chats available.",
    "Groupchat_cjql_b8e7010d": "Create Group",
    "Groupchat_create_cjql_a5211744": "Create Group",
    "Groupchat_create_cjsb_3da69005": "Failed to create.",
    "Groupchat_create_cxddsbqshzs_88172400": "Failed to find location. Try again later.",
    "Groupchat_create_dqcszwchd_2de2168e": "This event is not available in your city.",
    "Groupchat_create_grzyzs_c471ea06": "Show on Profile",
    "Groupchat_create_jdjsxqntldhtxwnxrjrql_895179dc": "Briefly explain what type of group chat this is. Include what you'll be discussing and the kind of people you'd like to see join.",
    "Groupchat_create_kqhqljzndgrzyzs_80e0a278": "Display this group on your profile",
    "Groupchat_create_ljcj_7f1dda54": "Create now",
    "Groupchat_create_myzdxgddhgcssb_f7a43a19": "No relevant locations found. Try another keyword.",
    "Groupchat_create_qjs_71ca1cca": "Group Bio",
    "Groupchat_create_qjszsxz100zyx_da56dfd7": "Group description must be under 100 characters.",
    "Groupchat_create_qlx_559f8a4e": "Group Category",
    "Groupchat_create_qmc_324eaba5": "Group Name",
    "Groupchat_create_qmczd12gz_beca06ab": "Enter up to 12 characters.",
    "Groupchat_create_qsr_bebe5355": "Enter",
    "Groupchat_create_qx_d90cd899": "Cancel",
    "Groupchat_create_syb_4a74221f": "Back",
    "Groupchat_create_tcshltjyd_18a6bff1": "Local life, social chats, and more.",
    "Groupchat_create_txzl_a9a993ad": "Enter your information",
    "Groupchat_create_wxz_2b248f06": "Not Selected",
    "Groupchat_create_wzdjghgwzhsscss_f09f63c6": "No results found – Try a different location or keyword.",
    "Groupchat_create_xyb_f55b078a": "Next",
    "Groupchat_create_xzdd_9757eea8": "Select a location",
    "Groupchat_dd": "Location:",
    "Groupchat_dqcszwchd_2de2168e": "This event is not available in your city.",
    "Groupchat_dqqydql_5c64e76a": "Local group chats",
    "Groupchat_dqqyzwql_b219d025": "No local group chats available.",
    "Groupchat_jlpx": "Sort by distance",
    "Groupchat_jq_949cf33e": "Create a group",
    "Groupchat_jqsqyfs": "Group request sent.",
    "Groupchat_jqsqyfs_eba1e8ba": "Group request sent.",
    "Groupchat_jr": "Join",
    "Groupchat_jr_0e8fc738": "Join",
    "Groupchat_kqdwfwkckfjdql": "Allow location services to discover nearby group chats.",
    "Groupchat_nybxzjqqx_7979ce5b": "Your account has been restricted",
    "Groupchat_poi_cjql_b8e7010d": "Create Group",
    "Groupchat_poi_dqqydql_a9c36ce8": "Local group chats",
    "Groupchat_poi_dqqyzwql_b219d025": "No local group chats available.",
    "Groupchat_poi_lwzj_a2ca3998": "Nearest to me",
    "Groupchat_poi_nybxzjqqx_7979ce5b": "Your account has been restricted",
    "Groupchat_poi_ztdtmbuseOnMapShowwxsl_d0d3753e": "[Themed Map Template] useOnMapShow, I disappeared",
    "Groupchat_poi_ztdtmbuseOnMapShowwzsl_9c2e009b": "[Themed Map Template] useOnMapShow, I appeared",
    "Groupchat_qcj": "Create",
    "Groupchat_qlt": "Chat",
    "Groupchat_qlt_18c6a66a": "Chat",
    "Groupchat_qsr": "Enter",
    "Groupchat_ss": "Search",
    "Groupchat_wzdjghgwzhsscss_f09f63c6": "No results found – Try a different location or keyword.",
    "Groupchat_yjddl": "You've reached the end.",
    "Groupchat_ymy": "Full",
    "Groupchat_ymy_1048dcfd": "Full",
    "Groupchat_ysq": "Requested",
    "Groupchat_ysq_9b25ec29": "Requested",
    "Groupchat_zhpx": "Default Sorting",
    "Groupchat_zndcscjygqlb": "Create a local group chat now.",
    "HalfLayer_FallbackText_No_Location": "No relevant locations in this area.",
    "HalfLayer_FallbackText_No_Notes": "No relevant posts in this area.",
    "HalfLayer_Fallback_Text_No_Live": "No suitable local live streams available.",
    "HalfLayer_Group_Chat_Tab_Create_Btn": "Create",
    "HalfLayer_Group_Chat_Tab_Create_Text": "Create a local group chat now.",
    "HalfLayer_Group_Chat_Tab_Join_Btn": "Join",
    "HalfLayer_Live_Tab_Viewer_Count_Label": " k",
    "HalfLayer_Note_Tag": "{{count}} notes",
    "HalfLayer_Note_Visited": "Checked in",
    "Limit_Notice_Running": "Route length less than 300m – Upload not supported",
    "Limit_Text": "Route length less than 1km – Upload not supported",
    "List_services_qbfl_3c744fbe": "All Categories",
    "List_services_qc_ec4c4758": "Citywide",
    "List_services_sx_0bc64569": "Filter",
    "List_services_zhpx_957ed521": "Default Sorting",
    "Location_Permit_Des": "Grant rednote to location permissions to track your activity footprint. Enable location privileges and set to Always.",
    "Location_Permit_Later": "Later",
    "Location_Permit_Set": "Set now",
    "Location_Permit_Title": "Allow location services at all times",
    "Lock_Screen_Toast": "Screen locked",
    "Map_BottomBar_HalfLayer_Tab_Group_Chat": "Group chat",
    "Map_BottomBar_HalfLayer_Tab_Live": "Live",
    "Map_BottomBar_HalfLayer_Tab_Notes": "Notes",
    "Map_BottomBar_HalfLayer_Tab_Place": "Spot",
    "Map_Bottom_Tab": "Explore this area",
    "Map_Group_Chat_Report": "If you suspect the group chat violate the platform's rules, you can report it.",
    "Map_Home_Capsule_Guide_Dating": "Date spots",
    "Map_Home_Capsule_Guide_Dining": "Group meals",
    "Map_Home_Capsule_Guide_Marked": "Collections",
    "Map_Home_Capsule_Guide_Movie": "Movies",
    "Map_Home_Capsule_Guide_Photo_Spot": "Photo spots",
    "Map_Home_Capsule_Guide_Sightseeing": "Fun",
    "Map_Home_Capsule_Guide_Visited": "Visited",
    "Map_Home_Capsule_Guideword_Bistro": "Midnight snacks",
    "Map_Home_Capsule_Guideword_Prayer": "Prayer",
    "Map_Home_Capsule_Guideword_Running": "Running",
    "Map_Home_Capsule_Guideword_Teatime": "Afternoon tea",
    "Map_Home_Marker_Subtext_Group_Chat_Count": "{{count}} group chats",
    "Map_Home_Marker_Subtext_Note_Count": "{{count}} notes",
    "Map_Interface_Bottom_Bar": "{{total}} locations {{total}} cities {{total}} routes",
    "Map_Search_Bar": "Search nearby locations",
    "Map_Search_Btn": "Search",
    "Map_Search_Complete": "Complete",
    "Map_Search_DeleteAll": "Delete all",
    "Map_Search_History": "History",
    "Map_Search_Nearby": "Explore nearby",
    "Map_Search_Note_Mention_Count": "Mentioned in {{count}} notes",
    "Map_Search_Page_Btn": "Search",
    "Map_Search_Quick_Access_Attraction": "Attractions",
    "Map_Search_Quick_Access_BBQ": "BBQ",
    "Map_Search_Quick_Access_Cafe": "Coffee",
    "Map_Search_Quick_Access_Camping": "Camping",
    "Map_Search_Quick_Access_Hotpot": "Hot pot",
    "Map_Search_Quick_Access_Park": "Parks",
    "Map_Search_Quick_Access_Restaurant": "Restaurants",
    "Map_Search_Quick_Access_Shopping": "Shopping",
    "Map_Search_Sort_All": "All Categories",
    "Map_Search_Sort_Comprehensive": "Default Sorting",
    "Month_Name_April": "April",
    "Month_Name_August": "August",
    "Month_Name_December": "December",
    "Month_Name_February": "February",
    "Month_Name_Jaunary": "January",
    "Month_Name_July": "July",
    "Month_Name_June": "June",
    "Month_Name_March": "March",
    "Month_Name_May": "May",
    "Month_Name_November": "November",
    "Month_Name_October": "October",
    "Month_Name_September": "September",
    "More_Bottom_Bar_Delete": "Delete",
    "More_Bottom_Bar_Edit": "Edit",
    "More_Bottom_Bar_Publish_Note": "Post Note",
    "More_Bottom_Bar_Title": "More",
    "Multi_Route_Creation_Hint": "Ideal for complex travel plans.",
    "Multi_Route_Preview_Location_Count": "{{count}} locations",
    "Multi_Route_Preview_Route_Count": "{{count}} routes",
    "Multi_Route_Select_Points_Confirm_Btn": "Complete",
    "Name_Input_Fail": "Review failed. Please check the track name.",
    "Name_Input_Hint": "Route name must be under 12 characters.",
    "Nearby_Tracks_Page_Intro": "Tracks in this area.",
    "No_Signal_Fallback_Text": "Server connection failed. Tap the button below to refresh.",
    "No_Signal_Refresh": "Refresh",
    "Note_Card_Saved_Time": "Collections",
    "POI_City_List_Fallback_Text": "You've reached the end.",
    "POI_Cmt_Text": "Got a question? Leave a comment!",
    "POI_Lable_Note_Count": "{{count}} notes",
    "POI_List_Title": "Related locations nearby",
    "POI_Loc_btn_text1": "Enable Location",
    "POI_Loc_btn_text2": "Get nearby content",
    "POI_Note_NoLiked_Text": "Like",
    "POI_Route_Cancel_Btn": "Cancel",
    "POI_Route_Est_Time": "Estimated",
    "POI_Route_Fallback_Drive": "Drive there",
    "POI_Route_Fallback_No_Info": "No public transportation info available.",
    "POI_Route_Fallback_Time": "{{time}} min",
    "POI_Route_Location": "My Location",
    "POI_Route_Nav_Start": "Start navigation",
    "POI_Route_Search": "Search nearby locations",
    "POI_Route_Total_Time": "Full Course",
    "POI_Route_Travel_Time_Hr": "h",
    "POI_Route_Travel_Time_Min": "m",
    "POI_StartChoice_Cancel": "Cancel",
    "Panel_Bar_Text": "Local routes and locations",
    "Panel_Nav_Bar_Text_Poi": "Cycling locations nearby",
    "Panel_Nav_Bar_Text_Poi_Running": "Running locations nearby",
    "Panel_Nav_Bar_Text_Track": "Cycling routes nearby",
    "Panel_Nav_Bar_Text_Track_Running": "Running routes nearby",
    "Pet_AdoptPet_Btn": "Adopt",
    "Pet_AdoptPet_Detail": "Let's explore the map and chat!",
    "Pet_AdoptPet_Title": "Adopt your exclusive pet!",
    "Pet_Info_Tab": "Cat/Dog/Rabbit/Dragon",
    "Pet_List_Fallback_Text": "No more pets in this area",
    "Pet_List_HalfLayer_Month_Count": "{{count}} months",
    "Pet_List_HalfLayer_Owner": "Pet Owner",
    "Pet_List_HalfLayer_Title": "All pets found in this area.",
    "Poi_End_Run_Distance_Img": "https://picasso-static.xiaohongshu.com/fe-platform/b46fd84017e0ddd9fd18efcae88e0e2ceb5c6491.png",
    "Poi_End_Run_Title_Img": "https://picasso-static.xiaohongshu.com/fe-platform/5c3128618f4f447d5bc2bc6d1cb0e3335392ff5a.png",
    "Poi_End_bicycle_Title_Img": "https://picasso-static.xiaohongshu.com/fe-platform/5c3128618f4f447d5bc2bc6d1cb0e3335392ff5a.png",
    "Poi_Point_end_run_img": "https://picasso-static.xiaohongshu.com/fe-platform/829f06a72b9814bde34937cc216c58977e4a77d5.png",
    "Poi_Point_strat_bicycle_img": "https://picasso-static.xiaohongshu.com/fe-platform/44bc90b493ec7ed8cac8f9e20941b656db574129.png",
    "Poi_Point_strat_run_img": "https://picasso-static.xiaohongshu.com/fe-platform/5bbe37ffbb6e894600eb2df0f0f8aa0e598eb6d9.png",
    "Poi_Route_Delete_Cancel": "Cancel",
    "Poi_Route_Delete_Confirm": "Confirm",
    "Poi_Route_Delete_Text": "Once deleted, the route cannot be recovered.",
    "Poi_Route_Delete_Title": "Delete",
    "Poi_Search_Label": "Search nearby locations",
    "Poi_Sport_L1_Img": "https://picasso-static.xiaohongshu.com/fe-platform/a3553cbc37fa1aa726adb1d9a563c7036818a78e.png",
    "Poi_Sport_L2_Img": "https://picasso-static.xiaohongshu.com/fe-platform/63926b59c83aed9a68b88ce64858aedd5d6d8c14.png",
    "Poi_Sport_L3_Img": "https://picasso-static.xiaohongshu.com/fe-platform/e5974c3f5fa434ad8e73086712108f628af6f829.png",
    "Poi_bcfq_b0f9a966": "Cancel",
    "Poi_btan_30643300": "Review Button",
    "Poi_cjhd_4b120688": "Create Event",
    "Poi_cjql_55e99306": "Create Group",
    "Poi_djzs_9e9122c8": "Tap to retry",
    "Poi_dqhdjdbfyhkfo_01e3508a": "This event is available to select users only.",
    "Poi_dqwlxhjcqshzso_51fcb763": "Weak network signal – Please try again later",
    "Poi_dwsb_05dd92ee": "Unable to determine location.",
    "Poi_fbj_07878426": "Publish note",
    "Poi_fz_a942a3ec": "Copy",
    "Poi_fzq_f729091d": "m ago",
    "Poi_gg_4db7e571": "Just now",
    "Poi_hd_7f976d83": "Delete",
    "Poi_hf_d5c14829": "Recover",
    "Poi_hqdwqxsb_2cdb772f": "Failed to obtain location permissions",
    "Poi_hqdwqxsb_e07e0f28": "Failed to obtain location permissions",
    "Poi_hqdwsb_432b5d1f": "Unable to obtain location.",
    "Poi_jd_d3228ada": "Attractions",
    "Poi_jdwl_ad460cc6": "Attractions",
    "Poi_jlty_100371c9": "Too far away.",
    "Poi_jyzj_f7d5e785": "Transaction setup",
    "Poi_kqdwqxcnwcqdo_1aed005b": "Enable location permissions to check in.",
    "Poi_kzwqgzzczdqdgddd_a1f82042": "Find your check-in locations under [Me] > [Visited]",
    "Poi_kzwscddzzczdscddd_a6c3d9cc": "Find your saved locations under [Me] > [Favorites] > [Places]",
    "Poi_kzzsjcblddzzczdzzncq_9438cd0d": "Go to [Sidebar] > [Orders] in the top left.",
    "Poi_ljkq_35f9b1e5": "Enable now",
    "Poi_ljlq_6256f5e9": "Claim Now",
    "Poi_lqjtyjsjhsqsjyyhx_703f1be3": "By claiming, you consent to the merchant using your phone number for verification.",
    "Poi_lqz_6ce08c78": "Claiming...",
    "Poi_ms_9d53de5a": "Food",
    "Poi_myzdhsddd_21160d97": "No suitable locations found.",
    "Poi_ncqzn_134fdd68": "Where are my tea coupons?",
    "Poi_nearby": "Nearby",
    "Poi_qbt_d2cca6e7": "Review",
    "Poi_qd_723173fa": "Check in",
    "Poi_qdcg_7de0c6ee": " Check-in successful!",
    "Poi_qdddnlz_8eb1bebb": "Where to Find Check-In Locations",
    "Poi_qdsczcqdm_e5655235": "Delete this check-in?",
    "Poi_qdz_977152d5": "Checking in...",
    "Poi_qiandaoshibai": "☹️ Check-in failed!",
    "Poi_qqsb_a35e9b0c": "Request failed.",
    "Poi_qqwxtszzkqdwqx_5a720e4f": "Go to system settings to enable location permissions.",
    "Poi_qrz_cec6f37a": "Join now",
    "Poi_qx_d90cd899": "Cancel",
    "Poi_qxrzcwhdzlr_fc8de43e": "Join as an event organizer first",
    "Poi_rzcwhdzlrhckyfbzjhdo_de780dc8": "Become an event organizer to start group events.",
    "Poi_sc_5de8806a": "Delete",
    "Poi_sccg_58c35654": "Added to Collections",
    "Poi_sccg_6a204606": "Deleted",
    "Poi_scqd_5319e40e": "Delete check-in",
    "Poi_ssfjdd_583f5479": "Search nearby locations",
    "Poi_sx_eba6b008": "Refresh",
    "Poi_tc_5b6601f8": "Set meal",
    "Poi_tq_ffc3a0ed": "day(s) ago",
    "Poi_wl_835e8318": "Entertainment",
    "Poi_wljdfwqsxyxss_029aac9d": "Server connection failed. Try refreshing.",
    "Poi_wzcw_f4a7e26f": "Unknown error.",
    "Poi_wzdl_0adc5047": "Got it",
    "Poi_wzdl_8ff9e783": " Got it",
    "Poi_xsq_382f31a0": "h ago",
    "Poi_yd_10b7def8": "Book",
    "Poi_yd_97a182b1": "Sports",
    "Poi_yhzs_8611752f": "Later",
    "Poi_yjddl_59d21696": "You've reached the end.",
    "Poi_yqd_14f636d7": " Checked in",
    "Poi_zddfj1kmncnwcqdo_5265e7a7": "You need to be within 1km to check in.",
    "Poi_zddfj500mncnwcqdo": "在地点附近500m内才能完成签到哦",
    "Poi_zjdzlss_c6738ec3": " Get closer and try again.",
    "Poi_zkcountthf_3d4b7cbc": "View {{count}} replies",
    "Poi_zkgdhf_e8ced8f0": "Show more replies",
    "Poi_zt_7788abb7": "Yesterday",
    "Poi_zx_68cd132d": "Support",
    "Preview_Running_Text": "Runners",
    "Preview_Text": "Cyclists",
    "Privacy_Notice_Toast": "Displays virtual pet location without revealing the owner's real location.",
    "Record_Repetition_Remind": "This record has been submitted, no need to upload again",
    "Return_Popup_Back": "Back to Edit",
    "Return_Popup_Exit_Without_Save": "Exit Directly",
    "Return_Popup_Save_Exit": "Save and exit",
    "Route_Add_Count_Limit": "Please add at least two locations",
    "Route_Create_Details_Bottom_Bar_Title": "Route Details • {{count}} Locations",
    "Route_Create_Details_HalfLayer_Add_Point_Btn": "Continue Adding Locations",
    "Route_Create_Details_HalfLayer_Distance": "km",
    "Route_Create_Details_HalfLayer_Time": "m",
    "Route_Create_Details_HalfLayer_Title": "Route Details",
    "Route_Creation_Location_Index": "Location {{number}}",
    "Route_Creation_Multi_Route_Btn": "Multi-route",
    "Route_Creation_Page_Location": "Spot",
    "Route_Creation_Page_Route": "Routes",
    "Route_Creation_Route_Index": "Route {{number}}",
    "Route_Creation_Single_Route": "Single route",
    "Route_Details_HalfLayer_Summary": "Includes {{count}} locations – Estimated travel time: {{time}}",
    "Route_Details_HalfLayer_Title": "Route Details",
    "Route_Edit": "Edit route",
    "Route_Edit_Outline": "Overview",
    "Route_Edit_Outline_CountLocation": "and {{count}} other locations",
    "Route_Mark_Cancel_Cancel": "Cancel",
    "Route_Mark_Cancel_Confirm": "Confirm",
    "Route_Mark_Cancel_Text": "We will save your route and return to the publish page. This route will not be displayed in the notes.",
    "Route_Mark_Cancel_Title": "Cancel route marking",
    "Route_Name_Edit_HalfLayer_Cancel": "Cancel",
    "Route_Name_Edit_HalfLayer_Confirm": "Complete",
    "Route_Name_Edit_HalfLayer_Guide": "Name this route",
    "Route_Name_Edit_HalfLayer_Name": "Route Name",
    "Route_Name_Edit_Limit": "Trip name must be under 12 characters.",
    "Route_Name_Edit_Limit_1": "Route name must be under 12 characters.",
    "Route_Name_Edit_Limit_2": "Route name must be under 8 characters.",
    "Route_Name_Empty": "Please enter content",
    "Route_Outline_Card_Count_Locations": "{{count}} locations",
    "Route_Outline_Locations_Count": "{{count}} locations",
    "Route_Select_Points_Add": "Add Location",
    "Route_Select_Points_Add_Location_Btn": "Add Location",
    "Route_Select_Points_Confirm_Btn": "Complete",
    "Route_Select_Points_Details": "Route Details",
    "Route_Select_Points_HalfLayer_Naming": "Untitled Route",
    "Route_Upload_Btn": "Upload route",
    "Route_Uploaded": "Uploaded",
    "RunRoute_HalfLayer_Text": "Tracks and locations nearby",
    "Run_Back_Remind": "Exiting will end your activity and save your log. Confirm?",
    "Run_Complete_Popup_Calories": "Calories burned",
    "Run_Complete_Toast_Distance": "Running Distance: {{distance}}",
    "Run_Complete_Toast_Duration": "Exercise Duration",
    "Run_Continue": "Continue",
    "Run_Details_Join_Cycling": "GO",
    "Run_Details_Join_GroupChat": "Group chat",
    "Run_Details_Join_Running": "GO",
    "Run_Details_Route_Difficulty": "Difficulty",
    "Run_Details_Route_Length": "Total length",
    "Run_Distance_Reminder_Countinue": "Continue activity",
    "Run_Distance_Reminder_End": "End",
    "Run_End": "End activity",
    "Run_End_Btn": "Restore",
    "Run_End_Distance": "Congrats! You ran {{distance}} km!",
    "Run_End_General": "Activity time or distance too short to save. End anyway?",
    "Run_End_Pace": "AVE",
    "Run_End_Quickest_Pace": "MAX",
    "Run_End_Stats_Cost": "Burned",
    "Run_End_Stats_Cost_Unit": "Calories",
    "Run_End_Stats_Duration": "Exercised for",
    "Run_End_Stats_Duration_Desc": "min",
    "Run_Long_Press_Unlock_Btn": "Long Press",
    "Run_Pause_Btn": "Pause",
    "Run_Pause_Toast": "Activity paused",
    "Run_Publish_Note_Btn": "Post Note",
    "Run_Records_Entrance_Btn": "My",
    "Run_Records_Page_Calories": "|",
    "Run_Records_Page_Calories_Unit": "Kilocalories",
    "Run_Records_Page_Difficulty": "Difficulty",
    "Run_Records_Page_Empty": "No activity logs yet.",
    "Run_Records_Page_Pace": "Pace",
    "Run_Records_Page_Participant_Count": "Used by {{count}} runners",
    "Run_Records_Page_Speed": "Speed",
    "Run_Records_Page_Time_Used": "Time",
    "Run_Records_Page_Title": "My Records",
    "Run_Remind": "Notice",
    "Run_Resume_Btn": "Long Press",
    "Run_Start_Btn": "Start now",
    "Run_Stats_Calories": "Calories burned",
    "Run_Stats_Duration": "Duration",
    "Run_Stats_Pace": "Current pace",
    "Run_Upload_Page_Fallback_Text": "Run over 400m to upload the track.",
    "Run_Upload_Page_Title": "My Uploads",
    "Run_Upload_Page_Upload_Btn": "Upload Now",
    "Run_Upload_Track_Btn": "Upload track",
    "Running_End_End": "Destination",
    "Running_End_Loading": "Finalizing...",
    "Running_End_Start": "from",
    "Running_Page_Title": "Running",
    "Saved_Locations_List_Title": "All saved locations in this area",
    "Search_Box_Query_Guide_Text": "Search",
    "Search_Dropdown_Filter_Nearby_Rec": "Smart recommendations nearby",
    "Search_Dropdown_Sort_Comprehensive": "Default Sorting",
    "Search_Dropdown_Sort_Distance_First": "Sort by Distance",
    "Search_Dropdown_Sort_Popularity_First": "Sort by popularity",
    "Search_Fail_Toast": "No results found – Try a different location or keyword.",
    "Search_Filter_Sort_Bar_Local": "Citywide",
    "Search_djzs_9e9122c8": "Tap to retry",
    "Search_lsjl_cc7ee1d6": "History",
    "Search_myzdxgddhgcssb_f7a43a19": "No relevant locations found. Try another keyword.",
    "Search_qbsc_2f34fa8a": "Delete all",
    "Search_ss_c548cb4c": "Search",
    "Search_ssfjdd_583f5479": "Search nearby locations",
    "Search_wc_b8750ff6": "Complete",
    "Search_wfljwlqshzs_664994dd": "No network connection. Try again later.",
    "Search_yjddl_59d21696": "You've reached the end.",
    "Selector_fl_1bae9ec2": "Category",
    "Selector_jl_f736692d": "Distance",
    "Selector_sx_0bc64569": "Filter",
    "Selector_wc_d99c0983": "Complete",
    "Selector_wzjl_7095fc16": "Location distance",
    "Selector_zntj_54d12377": "Smart recommendations",
    "Selector_zz_9864c39c": "Clear",
    "Share_Private_Title": "Share style",
    "Share_Settings_Confirm_Btn": "Confirm",
    "Share_Settings_Page_Title": "Sharing Settings",
    "Share_Settings_Privacy_Mode": "Privacy Mode",
    "Share_Settings_Standard_Mode": "Standard Mode",
    "Single_Route_Creation_Btn": "Create",
    "Single_Route_Creation_Hint": "Ideal for simple trips",
    "Speed_Text": "AVE",
    "Speed_Text_Running": "AVE",
    "Start_Running": "GO",
    "Start_Text": "GO",
    "Track_Desc_Guide_Text": "Describe the track for more map exposure.",
    "Track_Feature_1": "Scenic Views",
    "Track_Feature_2": "Shaded Paths",
    "Track_Feature_3": "Dance of Light and Silhouettes",
    "Track_Feature_4": "Floral Beauty",
    "Track_Feature_5": "Picturesque Views",
    "Track_Feature_6": "Refreshing Autumn Air",
    "Track_Type_1": "Rubber Track",
    "Track_Type_2": "Urban Roads",
    "Track_Type_3": "Natural Trails",
    "Track_Upload_Complete_Btn": "Complete",
    "Track_Upload_Review_Text": "Post a note and get some followers!",
    "Trip_Name_Edit_HalfLayer_Cancel": "Trip Name",
    "Trip_Name_Edit_HalfLayer_Confirm": "Complete",
    "Trip_Name_Edit_HalfLayer_Guide": "Name this route",
    "Trip_Name_Edit_HalfLayer_Title": "Cancel",
    "Upload_Empty": "Upload your first route!",
    "Upload_Success_Text": "Route uploaded!",
    "Upload_Track_Btn": "Upload Now",
    "Upload_Track_Difficulty_1": "Easy",
    "Upload_Track_Difficulty_2": "Normal",
    "Upload_Track_Difficulty_3": "Hard",
    "Upload_Track_Features": "Features",
    "Upload_Track_Page_Difficulty_Select": "Difficulty",
    "Upload_Track_Page_Length": "Track length",
    "Upload_Track_Page_Name": "Track name",
    "Upload_Track_Page_Tags": "Track tags",
    "Upload_Track_Type": "Type",
    "addDialogTitle_poi": "Exit now? Changes won't be saved.",
    "all_size_limit_lower_poi": ". Please reselect.",
    "all_size_limit_prefix_poi": "Image exceeds size limit.",
    "all_size_limit_suffix_poi": ". Please reselect.",
    "all_size_limit_upper_poi": "Image exceeds size limit.",
    "bad_declaretag_poi": "Bad",
    "cailei_declaretag_poi": "Risk Alerts",
    "confirm_delete_declare_poi": "Delete review?",
    "declared_poi": "Reviewed",
    "dialogCancelText_poi": "Cancel",
    "dialogConfirmText_poi": "Confirm",
    "good_declaretag_poi": "Nice",
    "great_declaretag_poi": "Great",
    "modifiedDialogTitle_poi": "Changes will not be saved after exiting.",
    "multiple_maps_tagged_locations": "Tagged Locations",
    "noScore_poi": "No star rating given yet.",
    "no_permission_poi": "Check your camera and album permissions.",
    "normal_declaretag_poi": "Just okay",
    "overHardLimit_poi": "Character limit reached.",
    "overLimit_poi": "Max 300 characters allowed.",
    "panelBtnText_poi": "That's all.",
    "panelPlaceholder_poi": "Share real experiences",
    "panelTitle_poi": "Reviews",
    "pic_num_limit_lower_poi": "Upload your favorite photo.",
    "pic_num_limit_upper_poi": "Select",
    "pic_size_limit_poi": "Cannot add any more images.",
    "poi_check_in_fail_toast": "【英语】签到失败，请检查网络和定位后重试",
    "poi_detail_activities": "Activities",
    "poi_detail_activity": "Activities",
    "poi_detail_all": "All",
    "poi_detail_application_sent": "The application has been sent. Please wait patiently.",
    "poi_detail_application_sent_msg": "Request sent... Please wait!",
    "poi_detail_applied": "Applied",
    "poi_detail_book": "Book",
    "poi_detail_booking": "Booking",
    "poi_detail_buy": "Buy",
    "poi_detail_chat": "Chat",
    "poi_detail_checkin": "Check In",
    "poi_detail_cmts": "Cmts",
    "poi_detail_comments": "Comments",
    "poi_detail_count_reviews": " {{ count }} Reviews",
    "poi_detail_create": "Create",
    "poi_detail_create_activities": "Come and create an activity",
    "poi_detail_create_here": "Create Here",
    "poi_detail_creategroup": "Come and create a group chat ",
    "poi_detail_experience": "Experiences",
    "poi_detail_exps": "Exps",
    "poi_detail_free": "Free",
    "poi_detail_full": "Full",
    "poi_detail_group": "Groups",
    "poi_detail_join": "Join",
    "poi_detail_joinn": "Join",
    "poi_detail_no_more_posts": "No more posts",
    "poi_detail_no_posts_yet": "No posts yet",
    "poi_detail_offer": "Offers",
    "poi_detail_post": "Posts",
    "poi_detail_recommendation": "Recommendations",
    "poi_detail_recs": "Recs",
    "poi_detail_redeem": "Redeem",
    "poi_detail_reply": "Reply",
    "poi_detail_review": "Review",
    "poi_detail_specialoffer": "Special Offers",
    "poi_detail_theend": "The End",
    "poi_detail_view": "View",
    "poi_group_chat_jy31145": "Request sent... Please wait!",
    "poi_start_bicycle": "Start",
    "poi_start_run": "Start",
    "poi_test": "321321",
    "select_failed_poi": "Image selection failed. Check Camera and Album permissions.",
    "size_test_failed_poi": "Failed to read image size. Please check it manually.",
    "some_size_limit_lower_poi": ". Deleted for your convenience.",
    "some_size_limit_prefix_poi": "Certain images exceed the size limit.",
    "some_size_limit_suffix_poi": ". Deleted for your convenience.",
    "some_size_limit_upper_poi": "Certain images exceed the size limit.",
    "toast_network_error_387": "Internet connection error.",
    "toast_network_error_poi_387": "Internet connection error.",
    "upload_failed_poi": "Image upload failed. Retry or compress it!",
    "upload_timeout_poi": "Upload failed. Try again.",
    "uploading_poi": "Wait for the image to upload before posting."
  }
}
export default Resources